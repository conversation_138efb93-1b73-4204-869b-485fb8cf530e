apiVersion: v1
kind: ConfigMap
metadata:
  name: frontend-nginx-config
  namespace: default
data:
  default.conf: |
    server {
        listen 80;
        server_name agentq.id www.agentq.id;

        # ... (Blok 0, 1, 2, 3 - GitHub Pages - tidak berubah) ...
        location = /signin { return 301 /login; }
        location = / { return 301 /agentq/; }
        location /agentq/ {
            proxy_pass https://agentq-ai.github.io/agentq/;
            proxy_set_header Host               agentq-ai.github.io;
            # ... (lainnya)
        }
        location /docs/ {
            rewrite ^/docs/(.*)$ /agentq/docs/$1 break;
            proxy_pass https://agentq-ai.github.io;
            proxy_set_header Host               agentq-ai.github.io;
            # ... (lainnya)
        }

        ####################################################################
        # 4) PENANGANAN RUTE DASHBOARD (App Frontend AgentQ)
        ####################################################################
        # Dashboard route and all dashboard sub-routes - must be before general assets
        location / {
            proxy_pass http://app-frontend-agentq-svc:80;
            proxy_set_header Host app-frontend-agentq-svc;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
        }

        ####################################################################
        # 5) PENANGANAN ASET SPA
        ####################################################################
        # Static files for dashboard (logo, favicon, etc.) - serve from dashboard app when in dashboard context
        location ~ ^/(logo\.png|agentq-32x32\.png|vite\.svg|favicon\.ico)$ {
            # Default to frontend-agentq-svc for login/billing pages
            set $target_service frontend-agentq-svc;

            # If request comes from dashboard or has dashboard in path, use dashboard service
            if ($http_referer ~ "^https?://[^/]+/dashboard") {
                set $target_service app-frontend-agentq-svc;
            }
            if ($request_uri ~ "^/dashboard") {
                set $target_service app-frontend-agentq-svc;
            }

            proxy_pass http://$target_service:80;
            proxy_set_header Host $target_service;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
            expires 30d;
            add_header Cache-Control "public, immutable";
        }

        # Assets for dashboard - route to correct service based on context
        location ^~ /assets/ {
            # Default to frontend-agentq-svc for login/billing assets
            set $target_service frontend-agentq-svc;

            # If request comes from dashboard or has dashboard in path, use dashboard service
            if ($http_referer ~ "^https?://[^/]+/dashboard") {
                set $target_service app-frontend-agentq-svc;
            }
            if ($request_uri ~ "^/dashboard") {
                set $target_service app-frontend-agentq-svc;
            }

            proxy_pass http://$target_service:80;
            proxy_set_header Host $target_service;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
            expires 30d;
            add_header Cache-Control "public, immutable";
        }

        ####################################################################
        # 6) PENANGANAN RUTE AUTH DAN BILLING
        ####################################################################
        # Auth callback routes
        location /auth/callback {
            proxy_pass http://frontend-agentq-svc:80;
            proxy_set_header Host frontend-agentq-svc;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;

            # Ensure query parameters are passed through
            proxy_pass_request_body on;
            proxy_pass_request_headers on;
        }

        # Login, signup, billing routes - frontend-agentq-svc
        location ~ ^/(login|signup|billing|payment|invoice|configuration|confirm-email|forgot-password|reset-password) {
            proxy_pass http://frontend-agentq-svc:80;
            proxy_set_header Host frontend-agentq-svc;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;

            # Handle SPA routing
            try_files $uri $uri/ @auth_fallback;
        }

        # Fallback for auth/billing SPA routes
        location @auth_fallback {
            proxy_pass http://frontend-agentq-svc:80;
            proxy_set_header Host frontend-agentq-svc;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
        }

        ####################################################################
        # 7) PENANGANAN RUTE SPA (Catch-all)
        ####################################################################
        location / {
            proxy_pass http://frontend-agentq-svc:80;

            # PERUBAHAN DI SINI JUGA:
            # proxy_set_header Host $host; # <--- JANGAN GUNAKAN INI
            proxy_set_header Host frontend-agentq-svc; # <--- GUNAKAN INI

            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
        }

        # 9) Optional global CORS (Tidak diubah)
        add_header Access-Control-Allow-Origin *;
    }
